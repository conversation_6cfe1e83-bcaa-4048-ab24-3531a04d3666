"use client";

import { useState, useEffect } from 'react';
import { StripeProvider } from './StripeProvider';
import { PaymentForm } from './PaymentForm';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle } from 'lucide-react';

interface PaymentWrapperProps {
  reservationId: string;
  amount: number; // in euros
  currency?: string;
  onSuccess: (paymentIntentId: string) => void;
  onError: (error: string) => void;
}

export function PaymentWrapper({
  reservationId,
  amount,
  currency = 'EUR',
  onSuccess,
  onError,
}: PaymentWrapperProps) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const createPaymentIntent = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/payments/create-intent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            reservationId,
          }),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to create payment intent');
        }

        if (result.success && result.clientSecret) {
          setClientSecret(result.clientSecret);
        } else {
          throw new Error('Invalid response from payment service');
        }
      } catch (err) {
        console.error('Error creating payment intent:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize payment';
        setError(errorMessage);
        onError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    if (reservationId) {
      createPaymentIntent();
    }
  }, [reservationId, onError]);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="w-6 h-6 animate-spin text-emerald-500 mr-3" />
            <span className="text-gray-600">Initialisation du paiement...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-red-600">Erreur de paiement</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!clientSecret) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Impossible d'initialiser le paiement. Veuillez réessayer.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <StripeProvider clientSecret={clientSecret}>
      <PaymentForm
        amount={amount}
        currency={currency}
        reservationId={reservationId}
        onSuccess={onSuccess}
        onError={onError}
      />
    </StripeProvider>
  );
}
