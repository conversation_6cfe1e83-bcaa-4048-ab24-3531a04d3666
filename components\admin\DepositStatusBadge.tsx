"use client";

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { CheckCircle, Clock, CreditCard, AlertCircle } from 'lucide-react';
import { formatAmount } from '@/lib/stripe';

interface DepositStatusBadgeProps {
  reservation: {
    total_amount: number;
    deposit_amount?: number | null;
    remaining_amount?: number | null;
    deposit_paid?: boolean | null;
    status: string;
  };
  payments?: Array<{
    amount: number;
    status: string;
    payment_type: string | null;
    is_deposit: boolean | null;
  }>;
}

export function DepositStatusBadge({ reservation, payments = [] }: DepositStatusBadgeProps) {
  const hasDepositPayment = payments.some(p => p.is_deposit && p.status === 'succeeded');
  const hasFullPayment = payments.some(p => p.payment_type === 'full' && p.status === 'succeeded');
  const totalPaid = payments
    .filter(p => p.status === 'succeeded')
    .reduce((sum, p) => sum + p.amount, 0);

  const getDepositStatus = () => {
    if (hasFullPayment) {
      return {
        type: 'full',
        label: 'Paiement intégral',
        variant: 'default' as const,
        icon: CheckCircle,
        color: 'text-emerald-600',
        bgColor: 'bg-emerald-100',
      };
    }

    if (hasDepositPayment) {
      const remainingAmount = reservation.total_amount - totalPaid;
      if (remainingAmount <= 0) {
        return {
          type: 'completed',
          label: 'Soldé',
          variant: 'default' as const,
          icon: CheckCircle,
          color: 'text-emerald-600',
          bgColor: 'bg-emerald-100',
        };
      } else {
        return {
          type: 'partial',
          label: 'Acompte payé',
          variant: 'secondary' as const,
          icon: Clock,
          color: 'text-orange-600',
          bgColor: 'bg-orange-100',
        };
      }
    }

    if (reservation.status === 'confirmed' || reservation.status === 'pending') {
      return {
        type: 'pending',
        label: 'En attente',
        variant: 'outline' as const,
        icon: CreditCard,
        color: 'text-gray-600',
        bgColor: 'bg-gray-100',
      };
    }

    return {
      type: 'none',
      label: 'Aucun paiement',
      variant: 'destructive' as const,
      icon: AlertCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    };
  };

  const status = getDepositStatus();
  const Icon = status.icon;

  const getTooltipContent = () => {
    const remainingAmount = reservation.total_amount - totalPaid;

    return (
      <div className="space-y-2">
        <div className="font-medium">Détails du paiement</div>
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span>Montant total:</span>
            <span>{formatAmount(reservation.total_amount * 100)}</span>
          </div>
          <div className="flex justify-between">
            <span>Montant payé:</span>
            <span>{formatAmount(totalPaid * 100)}</span>
          </div>
          {remainingAmount > 0 && (
            <div className="flex justify-between font-medium">
              <span>Solde restant:</span>
              <span>{formatAmount(remainingAmount * 100)}</span>
            </div>
          )}
        </div>
        {hasDepositPayment && remainingAmount > 0 && (
          <div className="text-xs text-orange-600 mt-2">
            Le solde restant doit être payé avant l'excursion
          </div>
        )}
      </div>
    );
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="inline-flex items-center">
            <Badge 
              variant={status.variant}
              className={`${status.bgColor} ${status.color} border-0 flex items-center gap-1`}
            >
              <Icon className="w-3 h-3" />
              {status.label}
            </Badge>
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          {getTooltipContent()}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Helper component for displaying payment summary
export function PaymentSummary({ reservation, payments = [] }: DepositStatusBadgeProps) {
  const totalPaid = payments
    .filter(p => p.status === 'succeeded')
    .reduce((sum, p) => sum + p.amount, 0);
  const remainingAmount = reservation.total_amount - totalPaid;

  return (
    <div className="space-y-2 text-sm">
      <div className="flex justify-between">
        <span className="text-gray-600">Total:</span>
        <span className="font-medium">{formatAmount(reservation.total_amount * 100)}</span>
      </div>
      <div className="flex justify-between">
        <span className="text-gray-600">Payé:</span>
        <span className="font-medium text-emerald-600">{formatAmount(totalPaid * 100)}</span>
      </div>
      {remainingAmount > 0 && (
        <div className="flex justify-between">
          <span className="text-gray-600">Restant:</span>
          <span className="font-medium text-orange-600">{formatAmount(remainingAmount * 100)}</span>
        </div>
      )}
    </div>
  );
}
