import { supabase } from './supabase';

// Database schema update functions using Supabase Management API
export const updateDatabaseSchema = async () => {
  try {
    console.log('Starting database schema updates for deposit system...');

    // Update payments table with deposit-related columns
    await updatePaymentsTable();
    
    // Update reservations table with deposit-related columns
    await updateReservationsTable();
    
    // Create payment audit trail table
    await createPaymentAuditTable();
    
    console.log('Database schema updates completed successfully');
    return { success: true };
  } catch (error) {
    console.error('Error updating database schema:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

// Update payments table with deposit-related columns
const updatePaymentsTable = async () => {
  const queries = [
    // Add payment_type column
    `ALTER TABLE payments ADD COLUMN IF NOT EXISTS payment_type TEXT CHECK (payment_type IN ('full', 'deposit', 'remaining'));`,
    
    // Add deposit_percentage column
    `ALTER TABLE payments ADD COLUMN IF NOT EXISTS deposit_percentage DECIMAL(5,2);`,
    
    // Add is_deposit column
    `ALTER TABLE payments ADD COLUMN IF NOT EXISTS is_deposit BOOLEAN DEFAULT FALSE;`,
    
    // Add manual_completion columns
    `ALTER TABLE payments ADD COLUMN IF NOT EXISTS manually_completed BOOLEAN DEFAULT FALSE;`,
    `ALTER TABLE payments ADD COLUMN IF NOT EXISTS completed_by TEXT;`,
    `ALTER TABLE payments ADD COLUMN IF NOT EXISTS completion_reason TEXT;`,
    `ALTER TABLE payments ADD COLUMN IF NOT EXISTS completion_date TIMESTAMPTZ;`,
    
    // Create index for payment_type
    `CREATE INDEX IF NOT EXISTS idx_payments_payment_type ON payments(payment_type);`,
    
    // Create index for is_deposit
    `CREATE INDEX IF NOT EXISTS idx_payments_is_deposit ON payments(is_deposit);`,
  ];

  for (const query of queries) {
    const { error } = await supabase.rpc('exec_sql', { sql: query });
    if (error) {
      console.error('Error executing payments table update:', error);
      throw error;
    }
  }
  
  console.log('Payments table updated successfully');
};

// Update reservations table with deposit-related columns
const updateReservationsTable = async () => {
  const queries = [
    // Add deposit_amount column
    `ALTER TABLE reservations ADD COLUMN IF NOT EXISTS deposit_amount DECIMAL(10,2);`,
    
    // Add remaining_amount column
    `ALTER TABLE reservations ADD COLUMN IF NOT EXISTS remaining_amount DECIMAL(10,2);`,
    
    // Add deposit_paid column
    `ALTER TABLE reservations ADD COLUMN IF NOT EXISTS deposit_paid BOOLEAN DEFAULT FALSE;`,
    
    // Add deposit_payment_id column
    `ALTER TABLE reservations ADD COLUMN IF NOT EXISTS deposit_payment_id TEXT REFERENCES payments(id);`,
    
    // Add remaining_payment_completed column
    `ALTER TABLE reservations ADD COLUMN IF NOT EXISTS remaining_payment_completed BOOLEAN DEFAULT FALSE;`,
    
    // Add remaining_payment_method column
    `ALTER TABLE reservations ADD COLUMN IF NOT EXISTS remaining_payment_method TEXT;`,
    
    // Create index for deposit_paid
    `CREATE INDEX IF NOT EXISTS idx_reservations_deposit_paid ON reservations(deposit_paid);`,
    
    // Create index for remaining_payment_completed
    `CREATE INDEX IF NOT EXISTS idx_reservations_remaining_completed ON reservations(remaining_payment_completed);`,
  ];

  for (const query of queries) {
    const { error } = await supabase.rpc('exec_sql', { sql: query });
    if (error) {
      console.error('Error executing reservations table update:', error);
      throw error;
    }
  }
  
  console.log('Reservations table updated successfully');
};

// Create payment audit trail table
const createPaymentAuditTable = async () => {
  const query = `
    CREATE TABLE IF NOT EXISTS payment_audit_trail (
      id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
      reservation_id TEXT NOT NULL REFERENCES reservations(id),
      payment_id TEXT REFERENCES payments(id),
      action_type TEXT NOT NULL CHECK (action_type IN ('manual_completion', 'status_change', 'refund', 'adjustment')),
      old_status TEXT,
      new_status TEXT,
      amount DECIMAL(10,2),
      admin_user_id TEXT,
      admin_user_email TEXT,
      reason TEXT,
      notes TEXT,
      metadata JSONB,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
  `;

  const { error } = await supabase.rpc('exec_sql', { sql: query });
  if (error) {
    console.error('Error creating payment audit trail table:', error);
    throw error;
  }

  // Create indexes
  const indexQueries = [
    `CREATE INDEX IF NOT EXISTS idx_payment_audit_reservation ON payment_audit_trail(reservation_id);`,
    `CREATE INDEX IF NOT EXISTS idx_payment_audit_payment ON payment_audit_trail(payment_id);`,
    `CREATE INDEX IF NOT EXISTS idx_payment_audit_action ON payment_audit_trail(action_type);`,
    `CREATE INDEX IF NOT EXISTS idx_payment_audit_admin ON payment_audit_trail(admin_user_id);`,
    `CREATE INDEX IF NOT EXISTS idx_payment_audit_created ON payment_audit_trail(created_at);`,
  ];

  for (const indexQuery of indexQueries) {
    const { error: indexError } = await supabase.rpc('exec_sql', { sql: indexQuery });
    if (indexError) {
      console.error('Error creating payment audit trail index:', indexError);
      throw indexError;
    }
  }
  
  console.log('Payment audit trail table created successfully');
};

// Helper function to check if schema updates are needed
export const checkSchemaUpdatesNeeded = async (): Promise<boolean> => {
  try {
    // Check if deposit-related columns exist in payments table
    const { data: paymentsColumns } = await supabase
      .rpc('get_table_columns', { table_name: 'payments' });
    
    const hasPaymentType = paymentsColumns?.some((col: any) => col.column_name === 'payment_type');
    const hasIsDeposit = paymentsColumns?.some((col: any) => col.column_name === 'is_deposit');
    
    // Check if deposit-related columns exist in reservations table
    const { data: reservationsColumns } = await supabase
      .rpc('get_table_columns', { table_name: 'reservations' });
    
    const hasDepositAmount = reservationsColumns?.some((col: any) => col.column_name === 'deposit_amount');
    const hasRemainingAmount = reservationsColumns?.some((col: any) => col.column_name === 'remaining_amount');
    
    // Check if audit trail table exists
    const { data: auditTable } = await supabase
      .rpc('check_table_exists', { table_name: 'payment_audit_trail' });
    
    return !hasPaymentType || !hasIsDeposit || !hasDepositAmount || !hasRemainingAmount || !auditTable;
  } catch (error) {
    console.error('Error checking schema updates needed:', error);
    return true; // Assume updates are needed if we can't check
  }
};

// Initialize schema updates if needed
export const initializeSchemaUpdates = async () => {
  try {
    const updatesNeeded = await checkSchemaUpdatesNeeded();
    
    if (updatesNeeded) {
      console.log('Schema updates needed, applying...');
      return await updateDatabaseSchema();
    } else {
      console.log('Schema is up to date');
      return { success: true, message: 'Schema is up to date' };
    }
  } catch (error) {
    console.error('Error initializing schema updates:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};
