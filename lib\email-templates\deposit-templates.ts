import { formatAmount } from '../stripe';

export interface DepositEmailData {
  customerName: string;
  reservationNumber: string;
  serviceName: string;
  serviceDate: string;
  serviceTime: string;
  participants: number;
  totalAmount: number;
  depositAmount: number;
  remainingAmount: number;
  depositPercentage: number;
  paymentDate: string;
}

// Deposit confirmation email template
export const generateDepositConfirmationEmail = (data: DepositEmailData): { subject: string; html: string; text: string } => {
  const subject = `Acompte confirmé - Réservation ${data.reservationNumber}`;

  const html = `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #10b981; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
        .success-badge { background-color: #10b981; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 20px 0; }
        .info-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .info-row { display: flex; justify-content: space-between; margin-bottom: 10px; padding: 5px 0; border-bottom: 1px solid #e9ecef; }
        .info-row:last-child { border-bottom: none; }
        .label { font-weight: bold; color: #6b7280; }
        .value { color: #111827; }
        .amount-highlight { background-color: #10b981; color: white; padding: 15px; border-radius: 8px; text-align: center; margin: 20px 0; }
        .remaining-notice { background-color: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; color: #6b7280; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Soleil & Découverte</div>
        <div>Excursions éco-responsables en Guadeloupe</div>
      </div>

      <div class="success-badge">✓ Acompte confirmé</div>

      <h2>Bonjour ${data.customerName},</h2>

      <p>Nous avons bien reçu votre acompte pour votre réservation. Voici les détails :</p>

      <div class="info-section">
        <div class="info-row">
          <span class="label">Numéro de réservation :</span>
          <span class="value">${data.reservationNumber}</span>
        </div>
        <div class="info-row">
          <span class="label">Excursion :</span>
          <span class="value">${data.serviceName}</span>
        </div>
        <div class="info-row">
          <span class="label">Date et heure :</span>
          <span class="value">${data.serviceDate} à ${data.serviceTime}</span>
        </div>
        <div class="info-row">
          <span class="label">Nombre de participants :</span>
          <span class="value">${data.participants}</span>
        </div>
        <div class="info-row">
          <span class="label">Date de paiement :</span>
          <span class="value">${data.paymentDate}</span>
        </div>
      </div>

      <div class="amount-highlight">
        <h3 style="margin: 0 0 10px 0;">Acompte payé : ${formatAmount(data.depositAmount * 100)}</h3>
        <p style="margin: 0; font-size: 14px;">sur un total de ${formatAmount(data.totalAmount * 100)}</p>
      </div>

      <div class="remaining-notice">
        <h4 style="margin: 0 0 10px 0; color: #92400e;">⚠️ Solde restant à payer</h4>
        <p style="margin: 0; color: #92400e;">
          <strong>${formatAmount(data.remainingAmount * 100)}</strong> restent à régler avant votre excursion.
          Vous pourrez payer le solde sur place ou nous contacter pour un paiement anticipé.
        </p>
      </div>

      <h3>Prochaines étapes :</h3>
      <ul>
        <li>Votre place est maintenant réservée</li>
        <li>Vous recevrez un rappel 24h avant votre excursion</li>
        <li>Préparez le solde restant (${formatAmount(data.remainingAmount * 100)}) pour le jour J</li>
        <li>Rendez-vous au point de départ 15 minutes avant l'heure prévue</li>
      </ul>

      <div class="footer">
        <p>Merci pour votre confiance !</p>
        <p>Pour toute question, contactez-nous à <EMAIL></p>
        <p style="margin-top: 20px;">
          Soleil & Découverte - Excursions éco-responsables<br>
          Guadeloupe, France
        </p>
      </div>
    </body>
    </html>
  `;

  const text = `
SOLEIL & DÉCOUVERTE
Excursions éco-responsables en Guadeloupe

✓ ACOMPTE CONFIRMÉ

Bonjour ${data.customerName},

Nous avons bien reçu votre acompte pour votre réservation.

DÉTAILS DE LA RÉSERVATION :
- Numéro de réservation : ${data.reservationNumber}
- Excursion : ${data.serviceName}
- Date et heure : ${data.serviceDate} à ${data.serviceTime}
- Nombre de participants : ${data.participants}
- Date de paiement : ${data.paymentDate}

ACOMPTE PAYÉ : ${formatAmount(data.depositAmount * 100)}
sur un total de ${formatAmount(data.totalAmount * 100)}

⚠️ SOLDE RESTANT À PAYER : ${formatAmount(data.remainingAmount * 100)}
Ce montant reste à régler avant votre excursion.
Vous pourrez payer le solde sur place ou nous contacter pour un paiement anticipé.

PROCHAINES ÉTAPES :
- Votre place est maintenant réservée
- Vous recevrez un rappel 24h avant votre excursion
- Préparez le solde restant pour le jour J
- Rendez-vous au point de départ 15 minutes avant l'heure prévue

Merci pour votre confiance !
Pour toute question, contactez-nous à <EMAIL>

Soleil & Découverte - Excursions éco-responsables
Guadeloupe, France
  `.trim();

  return { subject, html, text };
};

// Remaining balance reminder email template
export const generateRemainingBalanceReminderEmail = (data: DepositEmailData): { subject: string; html: string; text: string } => {
  const subject = `Rappel : Solde restant à payer - Réservation ${data.reservationNumber}`;

  const html = `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #10b981; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
        .reminder-badge { background-color: #f59e0b; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 20px 0; }
        .info-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .amount-highlight { background-color: #f59e0b; color: white; padding: 15px; border-radius: 8px; text-align: center; margin: 20px 0; }
        .payment-info { background-color: #e0f2fe; border: 1px solid #0284c7; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; color: #6b7280; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Soleil & Découverte</div>
        <div>Excursions éco-responsables en Guadeloupe</div>
      </div>

      <div class="reminder-badge">⏰ Rappel de paiement</div>

      <h2>Bonjour ${data.customerName},</h2>

      <p>Votre excursion approche ! Nous vous rappelons qu'un solde reste à payer pour votre réservation.</p>

      <div class="info-section">
        <h3>Détails de votre réservation :</h3>
        <p><strong>Réservation :</strong> ${data.reservationNumber}</p>
        <p><strong>Excursion :</strong> ${data.serviceName}</p>
        <p><strong>Date et heure :</strong> ${data.serviceDate} à ${data.serviceTime}</p>
        <p><strong>Participants :</strong> ${data.participants}</p>
      </div>

      <div class="amount-highlight">
        <h3 style="margin: 0 0 10px 0;">Solde à payer : ${formatAmount(data.remainingAmount * 100)}</h3>
        <p style="margin: 0; font-size: 14px;">Acompte déjà payé : ${formatAmount(data.depositAmount * 100)}</p>
      </div>

      <div class="payment-info">
        <h4 style="margin: 0 0 10px 0; color: #0369a1;">💳 Options de paiement</h4>
        <ul style="margin: 0; color: #0369a1;">
          <li>Paiement sur place le jour de l'excursion (espèces ou carte)</li>
          <li>Paiement anticipé en nous contactant</li>
          <li>Virement bancaire (nous contacter pour les détails)</li>
        </ul>
      </div>

      <h3>Informations importantes :</h3>
      <ul>
        <li>Rendez-vous 15 minutes avant l'heure prévue</li>
        <li>Apportez une pièce d'identité</li>
        <li>Prévoyez des vêtements adaptés à l'activité</li>
        <li>N'hésitez pas à nous contacter pour toute question</li>
      </ul>

      <div class="footer">
        <p>Nous avons hâte de vous accueillir !</p>
        <p>Pour toute question, contactez-nous à <EMAIL></p>
        <p style="margin-top: 20px;">
          Soleil & Découverte - Excursions éco-responsables<br>
          Guadeloupe, France
        </p>
      </div>
    </body>
    </html>
  `;

  const text = `
SOLEIL & DÉCOUVERTE
Excursions éco-responsables en Guadeloupe

⏰ RAPPEL DE PAIEMENT

Bonjour ${data.customerName},

Votre excursion approche ! Nous vous rappelons qu'un solde reste à payer pour votre réservation.

DÉTAILS DE VOTRE RÉSERVATION :
- Réservation : ${data.reservationNumber}
- Excursion : ${data.serviceName}
- Date et heure : ${data.serviceDate} à ${data.serviceTime}
- Participants : ${data.participants}

SOLDE À PAYER : ${formatAmount(data.remainingAmount * 100)}
Acompte déjà payé : ${formatAmount(data.depositAmount * 100)}

💳 OPTIONS DE PAIEMENT :
- Paiement sur place le jour de l'excursion (espèces ou carte)
- Paiement anticipé en nous contactant
- Virement bancaire (nous contacter pour les détails)

INFORMATIONS IMPORTANTES :
- Rendez-vous 15 minutes avant l'heure prévue
- Apportez une pièce d'identité
- Prévoyez des vêtements adaptés à l'activité
- N'hésitez pas à nous contacter pour toute question

Nous avons hâte de vous accueillir !
Pour toute question, contactez-nous à <EMAIL>

Soleil & Découverte - Excursions éco-responsables
Guadeloupe, France
  `.trim();

  return { subject, html, text };
};
