"use client";

import { useState } from 'react';
import {
  PaymentElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CreditCard, AlertCircle, CheckCircle } from 'lucide-react';
import { formatAmount } from '@/lib/stripe';

interface PaymentFormProps {
  amount: number; // in euros
  currency?: string;
  reservationId: string;
  onSuccess: (paymentIntentId: string) => void;
  onError: (error: string) => void;
  disabled?: boolean;
}

export function PaymentForm({
  amount,
  currency = 'EUR',
  reservationId,
  onSuccess,
  onError,
  disabled = false,
}: PaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isComplete, setIsComplete] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      setError('Stripe n\'est pas encore chargé. Veuillez réessayer.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Confirm payment
      const { error: submitError } = await elements.submit();
      if (submitError) {
        setError(submitError.message || 'Erreur lors de la soumission du paiement');
        setIsLoading(false);
        return;
      }

      const { error: confirmError, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/reservation/confirmation?booking=${reservationId}`,
        },
        redirect: 'if_required',
      });

      if (confirmError) {
        setError(confirmError.message || 'Erreur lors de la confirmation du paiement');
        onError(confirmError.message || 'Payment failed');
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        // Payment successful
        setIsComplete(true);
        
        // Confirm payment on our backend
        try {
          const response = await fetch('/api/payments/confirm', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              paymentIntentId: paymentIntent.id,
            }),
          });

          const result = await response.json();
          
          if (result.success) {
            onSuccess(paymentIntent.id);
          } else {
            setError(result.error || 'Erreur lors de la confirmation du paiement');
            onError(result.error || 'Payment confirmation failed');
          }
        } catch (confirmationError) {
          console.error('Error confirming payment:', confirmationError);
          setError('Erreur lors de la confirmation du paiement');
          onError('Payment confirmation failed');
        }
      }
    } catch (err) {
      console.error('Payment error:', err);
      setError('Une erreur inattendue s\'est produite');
      onError('Unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleElementChange = (event: any) => {
    if (event.error) {
      setError(event.error.message);
    } else {
      setError(null);
    }
  };

  if (isComplete) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <CheckCircle className="w-16 h-16 text-emerald-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-emerald-600 mb-2">
              Paiement réussi !
            </h3>
            <p className="text-gray-600">
              Votre paiement de {formatAmount(amount * 100)} a été traité avec succès.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="w-5 h-5" />
          Paiement sécurisé
        </CardTitle>
        <div className="text-sm text-gray-600">
          Montant à payer : <span className="font-semibold">{formatAmount(amount * 100)}</span>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="p-4 border rounded-lg bg-gray-50">
            <PaymentElement
              onChange={handleElementChange}
              options={{
                layout: 'tabs',
              }}
            />
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Button
              type="submit"
              disabled={!stripe || !elements || isLoading || disabled}
              className="w-full bg-emerald-500 hover:bg-emerald-600"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Traitement en cours...
                </>
              ) : (
                <>
                  <CreditCard className="w-4 h-4 mr-2" />
                  Payer {formatAmount(amount * 100)}
                </>
              )}
            </Button>
            
            <div className="text-xs text-gray-500 text-center">
              Paiement sécurisé par Stripe. Vos informations sont protégées.
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
