import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { confirmPaymentIntent, isPaymentSuccessful, isPaymentFailed } from '@/lib/stripe';
import { createNotification, createPaymentReceivedNotification } from '@/lib/notifications';

export async function POST(request: NextRequest) {
  try {
    const { paymentIntentId } = await request.json();

    if (!paymentIntentId) {
      return NextResponse.json(
        { error: 'Payment intent ID is required' },
        { status: 400 }
      );
    }

    // Confirm payment with Stripe
    const result = await confirmPaymentIntent(paymentIntentId);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to confirm payment' },
        { status: 500 }
      );
    }

    const { paymentIntent } = result;
    const paymentStatus = paymentIntent!.status;

    // Update payment record in database
    const updateData: any = {
      status: paymentStatus,
      updated_at: new Date().toISOString(),
    };

    if (isPaymentSuccessful(paymentStatus)) {
      updateData.payment_date = new Date().toISOString();
    } else if (isPaymentFailed(paymentStatus)) {
      updateData.failure_reason = paymentIntent!.last_payment_error?.message || 'Payment failed';
    }

    const { data: payment, error: paymentUpdateError } = await supabase
      .from('payments')
      .update(updateData)
      .eq('payment_intent_id', paymentIntentId)
      .select('*, reservation:reservations(*)')
      .single();

    if (paymentUpdateError) {
      console.error('Error updating payment record:', paymentUpdateError);
      return NextResponse.json(
        { error: 'Failed to update payment record' },
        { status: 500 }
      );
    }

    // If payment successful, update reservation status
    if (isPaymentSuccessful(paymentStatus)) {
      const { error: reservationUpdateError } = await supabase
        .from('reservations')
        .update({
          status: 'confirmed',
          confirmed_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', payment.reservation_id);

      if (reservationUpdateError) {
        console.error('Error updating reservation status:', reservationUpdateError);
        // Don't fail the request as payment was successful
      }

      // Create payment received notification for admins
      try {
        const { data: adminProfiles } = await supabase
          .from('profiles')
          .select('id')
          .eq('role', 'admin');

        if (adminProfiles) {
          const notificationTemplate = createPaymentReceivedNotification(
            payment.amount,
            payment.currency,
            payment.reservation_id
          );

          for (const admin of adminProfiles) {
            await createNotification(admin.id, notificationTemplate, payment.reservation_id);
          }
        }
      } catch (notificationError) {
        console.error('Error creating payment notification:', notificationError);
        // Don't fail the request
      }

      // Add reservation status history
      try {
        await supabase
          .from('reservation_status_history')
          .insert({
            reservation_id: payment.reservation_id,
            old_status: 'pending',
            new_status: 'confirmed',
            changed_by: null, // System change
            change_reason: 'Payment confirmed',
          });
      } catch (historyError) {
        console.error('Error adding status history:', historyError);
        // Don't fail the request
      }
    }

    return NextResponse.json({
      success: true,
      paymentStatus,
      paymentIntentId,
      reservationId: payment.reservation_id,
      amount: payment.amount,
      currency: payment.currency,
      isSuccessful: isPaymentSuccessful(paymentStatus),
      isFailed: isPaymentFailed(paymentStatus),
    });

  } catch (error) {
    console.error('Error confirming payment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
